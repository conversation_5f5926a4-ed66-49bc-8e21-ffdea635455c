<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment') ?>" class="btn btn-secondary">
    ← Back to Assessment
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Upload Evidence
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Upload evidence file for milestone: <strong><?= esc($milestone['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Milestone Context Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                🎯
            </div>
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <?= esc($milestone['title']) ?>
                </h3>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Code: <?= esc($milestone['milestone_code']) ?> • Project: <?= esc($project['title']) ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload Form -->
<div class="card mb-xl">
    <div class="card-header">
        📎 Evidence File Upload
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/upload') ?>" enctype="multipart/form-data" class="evidence-upload-form">
            <?= csrf_field() ?>
            
            <!-- File Upload -->
            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Evidence File <span style="color: var(--brand-danger);">*</span>
                </label>
                <div style="border: 2px dashed var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-xl); text-align: center; background: var(--bg-tertiary);">
                    <input type="file" name="evidence_file" id="evidence_file" required accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt" style="display: none;" onchange="updateFileDisplay()">
                    <label for="evidence_file" style="cursor: pointer; display: block;">
                        <div style="font-size: 3rem; margin-bottom: var(--spacing-md); color: var(--text-muted);">📎</div>
                        <div style="color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-xs);">
                            Click to select file or drag and drop
                        </div>
                        <div style="color: var(--text-muted); font-size: 0.875rem;">
                            Maximum file size: 25MB
                        </div>
                    </label>
                    <div id="file-display" style="margin-top: var(--spacing-md); display: none;">
                        <div style="background: var(--bg-primary); border-radius: var(--radius-md); padding: var(--spacing-md); display: inline-block;">
                            <span id="file-name" style="color: var(--text-primary); font-weight: 600;"></span>
                            <span id="file-size" style="color: var(--text-muted); margin-left: var(--spacing-sm);"></span>
                        </div>
                    </div>
                </div>
                <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                    Supported formats: Images (JPG, PNG, GIF), Videos (MP4, AVI, MOV), Audio (MP3, WAV), Documents (PDF, DOC, XLS, PPT, TXT)
                </div>
            </div>

            <!-- File Type -->
            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    File Type <span style="color: var(--brand-danger);">*</span>
                </label>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-md);">
                    <!-- Image Type -->
                    <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                        <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                            <input type="radio" name="file_type" value="image" required style="margin-top: var(--spacing-xs);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    🖼️ Image
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                    Photos, screenshots, diagrams, or visual evidence of milestone completion.
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Document Type -->
                    <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                        <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                            <input type="radio" name="file_type" value="document" required style="margin-top: var(--spacing-xs);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    📄 Document
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                    Reports, certificates, forms, or written documentation related to the milestone.
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Video Type -->
                    <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                        <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                            <input type="radio" name="file_type" value="video" required style="margin-top: var(--spacing-xs);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    🎥 Video
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                    Video recordings showing milestone progress or completion activities.
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Audio Type -->
                    <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                        <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                            <input type="radio" name="file_type" value="audio" required style="margin-top: var(--spacing-xs);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    🎵 Audio
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                    Audio recordings, interviews, or sound documentation.
                                </div>
                            </div>
                        </label>
                    </div>

                    <!-- Other Type -->
                    <div style="border: 2px solid var(--border-color); border-radius: var(--radius-md); padding: var(--spacing-lg);">
                        <label style="display: flex; align-items: start; gap: var(--spacing-md); cursor: pointer;">
                            <input type="radio" name="file_type" value="other" required style="margin-top: var(--spacing-xs);">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                    📦 Other
                                </div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.5;">
                                    Other file types or specialized formats not covered above.
                                </div>
                            </div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- File Title -->
            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    File Title <span style="color: var(--brand-danger);">*</span>
                </label>
                <input type="text" name="title" value="<?= old('title') ?>" required maxlength="150" style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-danger); border-radius: var(--radius-md); font-family: inherit;" placeholder="Enter a descriptive title for this evidence file...">
                <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                    Maximum 150 characters
                </div>
            </div>

            <!-- File Description -->
            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Description
                </label>
                <textarea name="description" rows="4" style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--brand-secondary); border-radius: var(--radius-md); font-family: inherit; resize: vertical;" placeholder="Provide additional details about this evidence file..."><?= old('description') ?></textarea>
                <div style="font-size: 0.875rem; color: var(--text-muted); margin-top: var(--spacing-xs);">
                    Optional: Add context, notes, or explanations about this evidence
                </div>
            </div>

            <!-- Upload Guidelines -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                <div style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📋 Upload Guidelines
                </div>
                <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
                    <li>Ensure files clearly demonstrate milestone completion or progress</li>
                    <li>Use descriptive titles and provide context in descriptions</li>
                    <li>Files will be marked as "Pending Verification" until reviewed by administrators</li>
                    <li>Maximum file size is 25MB per upload</li>
                    <li>Multiple files can be uploaded for comprehensive evidence</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end; padding-top: var(--spacing-lg); border-top: 1px solid var(--border-color);">
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    📎 Upload Evidence
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function updateFileDisplay() {
    const fileInput = document.getElementById('evidence_file');
    const fileDisplay = document.getElementById('file-display');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        fileName.textContent = file.name;
        fileSize.textContent = '(' + (file.size / 1024 / 1024).toFixed(2) + ' MB)';
        fileDisplay.style.display = 'block';
        
        // Auto-select file type based on file extension
        const extension = file.name.split('.').pop().toLowerCase();
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        const audioExts = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
        const docExts = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        
        if (imageExts.includes(extension)) {
            document.querySelector('input[name="file_type"][value="image"]').checked = true;
        } else if (videoExts.includes(extension)) {
            document.querySelector('input[name="file_type"][value="video"]').checked = true;
        } else if (audioExts.includes(extension)) {
            document.querySelector('input[name="file_type"][value="audio"]').checked = true;
        } else if (docExts.includes(extension)) {
            document.querySelector('input[name="file_type"][value="document"]').checked = true;
        } else {
            document.querySelector('input[name="file_type"][value="other"]').checked = true;
        }
    } else {
        fileDisplay.style.display = 'none';
    }
}

// Form validation
document.querySelector('.evidence-upload-form').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('evidence_file');
    const title = document.querySelector('input[name="title"]').value;
    const fileType = document.querySelector('input[name="file_type"]:checked');
    
    if (!fileInput.files.length) {
        e.preventDefault();
        alert('Please select a file to upload.');
        return;
    }
    
    if (!title.trim()) {
        e.preventDefault();
        alert('Please enter a title for the evidence file.');
        return;
    }
    
    if (!fileType) {
        e.preventDefault();
        alert('Please select a file type.');
        return;
    }
    
    // Check file size (25MB = 25 * 1024 * 1024 bytes)
    if (fileInput.files[0].size > 25 * 1024 * 1024) {
        e.preventDefault();
        alert('File size must be less than 25MB.');
        return;
    }
});

// Drag and drop functionality
const dropZone = document.querySelector('.card');
dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.style.borderColor = 'var(--brand-primary)';
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.style.borderColor = 'var(--border-color)';
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    this.style.borderColor = 'var(--border-color)';
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('evidence_file').files = files;
        updateFileDisplay();
    }
});
</script>

<style>
/* Form validation styling */
.evidence-upload-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.evidence-upload-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.evidence-upload-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* File type radio styling */
input[type="radio"]:checked + div {
    color: var(--brand-primary);
}

input[type="radio"]:checked {
    accent-color: var(--brand-primary);
}
</style>

<?= $this->endSection() ?>
