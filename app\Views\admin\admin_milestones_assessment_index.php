<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>#project-milestones" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/upload') ?>" class="btn btn-primary">
    📎 Upload Evidence
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Milestone Assessment
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Evidence and verification for milestone: <strong><?= esc($milestone['title']) ?></strong>
        </p>
    </div>
</div>

<!-- Milestone Context Card -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; gap: var(--spacing-md);">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                🎯
            </div>
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                    <?= esc($milestone['title']) ?>
                </h3>
                <div style="color: var(--text-muted); font-size: 0.875rem; margin-top: var(--spacing-xs);">
                    Code: <?= esc($milestone['milestone_code']) ?> • Project: <?= esc($project['title']) ?>
                </div>
            </div>
            <div style="margin-left: auto;">
                <span style="background: <?= $milestone['status'] === 'completed' || $milestone['status'] === 'approved' ? 'var(--brand-success)' : ($milestone['status'] === 'in-progress' ? 'var(--brand-warning)' : 'var(--brand-secondary)') ?>; color: white; padding: var(--spacing-sm) var(--spacing-md); border-radius: var(--radius-md); font-size: 0.875rem; font-weight: 600; text-transform: uppercase;">
                    <?= esc($milestone['status']) ?>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Evidence Summary -->
<div class="card mb-xl">
    <div class="card-header">
        📊 Evidence Summary
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-lg);">
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-xs);">
                    <?= $fileStats['total_files'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Files</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-success); margin-bottom: var(--spacing-xs);">
                    <?= $fileStats['verified_files'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Verified</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-warning); margin-bottom: var(--spacing-xs);">
                    <?= $fileStats['unverified_files'] ?>
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Pending</div>
            </div>
            <div style="text-align: center; background: var(--bg-tertiary); padding: var(--spacing-lg); border-radius: var(--radius-md);">
                <div style="font-size: 1.5rem; font-weight: 700; color: var(--brand-info); margin-bottom: var(--spacing-xs);">
                    <?= $fileStats['total_size_mb'] ?>MB
                </div>
                <div style="color: var(--text-muted); font-size: 0.75rem;">Total Size</div>
            </div>
        </div>
    </div>
</div>

<!-- Evidence Gallery -->
<div class="card mb-xl">
    <div class="card-header">
        <div style="display: flex; align-items: center; justify-content: between; gap: var(--spacing-md);">
            <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                <span>📁</span>
                <span>Evidence Files</span>
            </div>
            <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/upload') ?>" class="btn btn-primary" style="margin-left: auto;">
                📎 Upload New Evidence
            </a>
        </div>
    </div>

    <div style="padding: var(--spacing-xl);">
        <?php if (!empty($evidenceFiles)): ?>
            <div style="display: grid; gap: var(--spacing-lg);">
                <?php foreach ($evidenceFiles as $file): ?>
                    <div style="background: var(--bg-tertiary); border-radius: var(--radius-md); padding: var(--spacing-lg); border-left: 4px solid <?= $file['is_verified'] ? 'var(--brand-success)' : 'var(--brand-warning)' ?>;">
                        <div style="display: flex; align-items: start; justify-content: between; gap: var(--spacing-md);">
                            <!-- File Info -->
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md); margin-bottom: var(--spacing-sm);">
                                    <h4 style="color: var(--text-primary); font-size: 1.125rem; font-weight: 600; margin: 0;">
                                        <?= esc($file['title']) ?>
                                    </h4>
                                    <?php if ($file['is_verified']): ?>
                                        <span style="background: var(--brand-success); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                            ✅ Verified
                                        </span>
                                    <?php else: ?>
                                        <span style="background: var(--brand-warning); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                            ⏳ Pending
                                        </span>
                                    <?php endif; ?>
                                    
                                    <span style="background: var(--brand-info); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                        <?= esc($file['file_type']) ?>
                                    </span>
                                </div>

                                <div style="display: grid; grid-template-columns: auto 1fr auto 1fr; gap: var(--spacing-sm) var(--spacing-md); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                                    <span style="color: var(--text-muted); font-weight: 500;">File Size:</span>
                                    <span style="color: var(--text-secondary);"><?= round($file['file_size'] / 1024, 2) ?>MB</span>

                                    <span style="color: var(--text-muted); font-weight: 500;">Uploaded:</span>
                                    <span style="color: var(--text-secondary);"><?= date('M j, Y H:i', strtotime($file['created_at'])) ?></span>

                                    <?php if ($file['is_verified'] && $file['verified_at']): ?>
                                        <span style="color: var(--text-muted); font-weight: 500;">Verified:</span>
                                        <span style="color: var(--brand-success);"><?= date('M j, Y H:i', strtotime($file['verified_at'])) ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($file['description']): ?>
                                    <p style="color: var(--text-secondary); margin-bottom: var(--spacing-sm); font-size: 0.875rem;">
                                        <?= esc($file['description']) ?>
                                    </p>
                                <?php endif; ?>

                                <?php if ($file['verification_notes']): ?>
                                    <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-md); margin-top: var(--spacing-sm);">
                                        <div style="font-size: 0.875rem; color: var(--brand-primary); font-weight: 600; margin-bottom: var(--spacing-xs);">
                                            📝 Verification Notes
                                        </div>
                                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                            <?= esc($file['verification_notes']) ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- File Actions -->
                            <div style="display: flex; flex-direction: column; gap: var(--spacing-sm);">
                                <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-secondary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    👁️ View
                                </a>
                                <button onclick="showVerifyModal(<?= $file['id'] ?>, '<?= esc($file['title']) ?>', <?= $file['is_verified'] ? 'true' : 'false' ?>, '<?= esc($file['verification_notes'] ?? '') ?>')" class="btn btn-primary" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    <?= $file['is_verified'] ? '✏️ Update' : '✅ Verify' ?>
                                </button>
                                <button onclick="showDeleteModal(<?= $file['id'] ?>, '<?= esc($file['title']) ?>')" class="btn btn-danger" style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;">
                                    🗑️ Delete
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: var(--spacing-xl); color: var(--text-muted);">
                <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">📁</div>
                <h3 style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">No Evidence Files</h3>
                <p style="margin-bottom: var(--spacing-lg);">Start documenting milestone completion by uploading evidence files.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/upload') ?>" class="btn btn-primary">
                    📎 Upload First Evidence
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Verify File Modal -->
<div id="verifyModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Verify Evidence File</h3>
        
        <form id="verifyForm" method="POST">
            <?= csrf_field() ?>
            <div style="margin-bottom: var(--spacing-lg);">
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-md);">
                    File: <strong id="verifyFileName"></strong>
                </p>
            </div>

            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Verification Status
                </label>
                <div style="display: flex; gap: var(--spacing-md);">
                    <label style="display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer;">
                        <input type="radio" name="is_verified" value="1" style="margin: 0;">
                        <span style="color: var(--brand-success); font-weight: 600;">✅ Verified</span>
                    </label>
                    <label style="display: flex; align-items: center; gap: var(--spacing-sm); cursor: pointer;">
                        <input type="radio" name="is_verified" value="0" style="margin: 0;">
                        <span style="color: var(--brand-warning); font-weight: 600;">⏳ Unverified</span>
                    </label>
                </div>
            </div>

            <div style="margin-bottom: var(--spacing-lg);">
                <label style="display: block; color: var(--text-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                    Verification Notes
                </label>
                <textarea name="verification_notes" rows="3" style="width: 100%; padding: var(--spacing-md); border: 2px solid var(--border-color); border-radius: var(--radius-md); font-family: inherit; resize: vertical;" placeholder="Add notes about the verification..."></textarea>
            </div>

            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
                <button type="button" onclick="hideVerifyModal()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="btn btn-primary">
                    💾 Save Verification
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete File Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Delete Evidence File</h3>
        
        <form id="deleteForm" method="POST">
            <?= csrf_field() ?>
            <div style="margin-bottom: var(--spacing-lg);">
                <p style="color: var(--text-secondary);">
                    Are you sure you want to delete <strong id="deleteFileName"></strong>?
                </p>
                <p style="color: var(--text-muted); font-size: 0.875rem;">
                    This action cannot be undone.
                </p>
            </div>

            <div style="display: flex; gap: var(--spacing-md); justify-content: flex-end;">
                <button type="button" onclick="hideDeleteModal()" class="btn btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="btn btn-danger">
                    🗑️ Delete File
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function showVerifyModal(fileId, fileName, isVerified, notes) {
    document.getElementById('verifyFileName').textContent = fileName;
    document.getElementById('verifyForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/') ?>' + fileId + '/verify';
    
    // Set verification status
    const verifiedRadio = document.querySelector('input[name="is_verified"][value="1"]');
    const unverifiedRadio = document.querySelector('input[name="is_verified"][value="0"]');
    
    if (isVerified) {
        verifiedRadio.checked = true;
    } else {
        unverifiedRadio.checked = true;
    }
    
    // Set notes
    document.querySelector('textarea[name="verification_notes"]').value = notes;
    
    document.getElementById('verifyModal').style.display = 'flex';
}

function hideVerifyModal() {
    document.getElementById('verifyModal').style.display = 'none';
}

function showDeleteModal(fileId, fileName) {
    document.getElementById('deleteFileName').textContent = fileName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/milestones/' . $milestone['id'] . '/assessment/') ?>' + fileId + '/delete';
    document.getElementById('deleteModal').style.display = 'flex';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

// Close modals when clicking outside
document.getElementById('verifyModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideVerifyModal();
    }
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideDeleteModal();
    }
});
</script>

<?= $this->endSection() ?>
